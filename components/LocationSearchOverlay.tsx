import React, { RefObject } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LocationSuggestion } from '@/api/location.api';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface LocationSearchOverlayProps {
  showSuggestions: boolean;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  suggestions: LocationSuggestion[];
  setSuggestions: (suggestions: LocationSuggestion[]) => void;
  setShowSuggestions: (show: boolean) => void;
  isSearchLoading: boolean;
  activeInput: 'pickup' | 'destination' | null;
  searchInputRef: RefObject<TextInput>;
  handleSelectLocation: (location: LocationSuggestion) => void;
}

const LocationSearchOverlay: React.FC<LocationSearchOverlayProps> = ({
  showSuggestions,
  searchQuery,
  setSearchQuery,
  suggestions,
  setSuggestions,
  setShowSuggestions,
  isSearchLoading,
  activeInput,
  searchInputRef,
  handleSelectLocation,
}) => {
  const insets = useSafeAreaInsets();

  if (!showSuggestions) {
    return null;
  }

  return (
    <View style={styles.searchOverlay}>
      <View
        style={[styles.searchHeader, { paddingTop: insets.top + 10 }]}
      >
        <TouchableOpacity
          style={styles.searchBackButton}
          onPress={() => {
            setShowSuggestions(false);
            setSearchQuery("");
            setSuggestions([]);
          }}
        >
          <Ionicons name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
        <View style={styles.searchInputWrapper}>
          <Ionicons
            name="search"
            size={20}
            color="#666"
            style={{ marginRight: 8 }}
          />
          <TextInput
            ref={searchInputRef}
            style={styles.searchInput}
            placeholder={
              activeInput === "pickup"
                ? "Tìm kiếm điểm đón..."
                : "Tìm kiếm điểm đến..."
            }
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => {
                setSearchQuery("");
              }}
            >
              <Ionicons name="close-circle" size={20} color="#999" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Suggestions list */}
      <View style={styles.suggestionsContainer}>
        {isSearchLoading ? (
          <ActivityIndicator
            size="small"
            color="#007AFF"
            style={styles.loader}
          />
        ) : (
          <>
            {searchQuery.length >= 5 && suggestions.length > 0 ? (
              <FlatList
                data={suggestions}
                keyExtractor={(item) => item.place_id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.suggestionItem}
                    onPress={() => handleSelectLocation(item)}
                  >
                    <Ionicons
                      name="location-outline"
                      size={18}
                      color="#666"
                      style={styles.suggestionIcon}
                    />
                    <Text
                      style={styles.suggestionText}
                      numberOfLines={2}
                    >
                      {item.description}
                    </Text>
                  </TouchableOpacity>
                )}
                style={styles.suggestionsList}
                nestedScrollEnabled
                maxToRenderPerBatch={5}
                initialNumToRender={5}
              />
            ) : searchQuery.length >= 5 ? (
              <View style={styles.noResultsContainer}>
                <Text style={styles.noResultsText}>
                  Không tìm thấy kết quả
                </Text>
              </View>
            ) : searchQuery.length > 0 ? (
              <View style={styles.noResultsContainer}>
                <Text style={styles.noResultsText}>
                  Nhập ít nhất 5 ký tự để tìm kiếm
                </Text>
              </View>
            ) : null}
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  searchOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'white',
    zIndex: 20,
  },
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchBackButton: {
    marginRight: 12,
    padding: 4,
  },
  searchInputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 4,
  },
  clearButton: {
    padding: 4,
  },
  suggestionsContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  suggestionsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionIcon: {
    marginRight: 12,
  },
  suggestionText: {
    flex: 1,
    fontSize: 15,
    color: '#333',
  },
  noResultsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  loader: {
    marginTop: 20,
  },
});

export default LocationSearchOverlay;
