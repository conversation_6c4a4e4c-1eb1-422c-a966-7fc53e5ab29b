import { getDirections, RouteInfo } from "@/api/direction.api";
import { getCoordinatesFromPlaceId } from "@/api/geocoding.api";
import GoongConstants from "@/constants/GoongConstants";
import { Ionicons } from "@expo/vector-icons";
import {
  Camera,
  LineLayer,
  MapView,
  ShapeSource,
} from "@maplibre/maplibre-react-native";
import { router, useLocalSearchParams } from "expo-router";
import { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Define interfaces for route data
interface LocationPoint {
  place_id: string;
  description: string;
}

interface RouteData {
  pickup: LocationPoint;
  destination: LocationPoint;
  stopPoints: LocationPoint[];
}

export default function App() {
  // Get route data from params
  const params = useLocalSearchParams<{ routeData: string }>();

  // Make sure GoongConstants is defined before using it
  const [loadMap] = useState(() => {
    if (!GoongConstants || !GoongConstants.MAP_TILES_URL || !GoongConstants.MAP_KEY) {
      console.error("GoongConstants is not properly defined:", GoongConstants);
      return "https://tiles.goong.io/assets/goong_map_web.json?api_key=H32hbWEAgDaXRTJzzT9hxHCU1S0YH9QxjX30SR5S";
    }
    return `${GoongConstants.MAP_TILES_URL}?api_key=${GoongConstants.MAP_KEY}`;
  });

  // State for loading status
  const [isLoading, setIsLoading] = useState(true);

  // Center coordinates for the map (longitude, latitude)
  const [coordinates, setCoordinates] = useState<[number, number]>([
    107.8091190568345, 11.54498921967426,
  ]);

  // Define map points with proper format (longitude, latitude)
  const [mapPoints, setMapPoints] = useState<
    {
      id: string;
      coordinates: [number, number];
      title: string;
    }[]
  >([]);

  // State for route information
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);

  const camera = useRef(null);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    const fetchCoordinates = async () => {
      try {
        setIsLoading(true);

        if (!params.routeData) {
          setIsLoading(false);
          return;
        }

        // Parse route data
        const routeData: RouteData = JSON.parse(
          decodeURIComponent(params.routeData)
        );

        // Get coordinates for pickup point
        const pickupCoords = await getCoordinatesFromPlaceId(
          routeData.pickup.place_id
        );

        // Get coordinates for destination point
        const destCoords = await getCoordinatesFromPlaceId(
          routeData.destination.place_id
        );

        if (!pickupCoords || !destCoords) {
          Alert.alert("Lỗi", "Không thể lấy tọa độ cho điểm đón hoặc điểm đến");
          setIsLoading(false);
          return;
        }

        // Create points array for markers
        const points = [
          {
            id: "pickup",
            coordinates: [pickupCoords.lng, pickupCoords.lat] as [
              number,
              number
            ],
            title: routeData.pickup.description,
          },
          {
            id: "destination",
            coordinates: [destCoords.lng, destCoords.lat] as [number, number],
            title: routeData.destination.description,
          },
        ];

        // Calculate midpoint between pickup and destination
        const midLng = (pickupCoords.lng + destCoords.lng) / 2;
        const midLat = (pickupCoords.lat + destCoords.lat) / 2;

        // Set center coordinates to midpoint
        setCoordinates([midLng, midLat]);

        // Get directions from Goong API
        const directions = await getDirections(
          [pickupCoords.lat, pickupCoords.lng],
          [destCoords.lat, destCoords.lng],
          "car"
        );

        if (directions) {
          // Store route information
          setRouteInfo(directions);

          // Set map points
          setMapPoints(points);
        } else {
          // If directions API fails, fallback to straight line
          setMapPoints(points);
          Alert.alert(
            "Thông báo",
            "Không thể lấy thông tin tuyến đường. Hiển thị đường thẳng."
          );
        }

        // const drivers = [
        //   {
        //     id: "driver1",
        //     coordinates: [
        //       pickupCoords.lng + (Math.random() - 0.5) * 0.01,
        //       pickupCoords.lat + (Math.random() - 0.5) * 0.01,
        //     ] as [number, number],
        //     title: "Tài xế Nguyễn Văn A",
        //     type: "car" as const,
        //   },
        //   {
        //     id: "driver2",
        //     coordinates: [
        //       midLng + (Math.random() - 0.5) * 0.01,
        //       midLat + (Math.random() - 0.5) * 0.01,
        //     ] as [number, number],
        //     title: "Tài xế Trần Văn B",
        //     type: "motorcycle" as const,
        //   },
        //   {
        //     id: "driver3",
        //     coordinates: [
        //       destCoords.lng + (Math.random() - 0.5) * 0.01,
        //       destCoords.lat + (Math.random() - 0.5) * 0.01,
        //     ] as [number, number],
        //     title: "Tài xế Lê Văn C",
        //     type: "truck" as const,
        //   },
        // ];

        // setDriverLocations(drivers);
      } catch (error) {
        console.error("Error fetching coordinates:", error);
        Alert.alert("Lỗi", "Đã xảy ra lỗi khi tải dữ liệu bản đồ");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCoordinates();
  }, [params.routeData]);

  const handleBack = () => {
    router.back();
  };

  return (
    <View style={{ flex: 1 }}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Đang tải bản đồ...</Text>
        </View>
      ) : (
        <>
          <MapView
            styleURL={loadMap}
            style={{ flex: 1 }}
            zoomEnabled={true}
            logoEnabled={false}
            onDidFailLoadingMap={() => {
              console.error("MapView failed to load");
              Alert.alert("Lỗi bản đồ", "Không thể tải bản đồ. Vui lòng thử lại sau.");
            }}
          >
            <Camera
              ref={camera}
              zoomLevel={13} // Mức thu phóng của bản đồ
              centerCoordinate={coordinates}
              animationDuration={0}
            />

            {/* tài xế gần đây */}
            {/* {driverLocations.map((driver) => (
              <PointAnnotation
                key={driver.id}
                id={driver.id}
                coordinate={driver.coordinates}
              >
                <View style={styles.driverMarker}>
                  {driver.type === "car" && (
                    <Ionicons name="car" size={12} color="#FF6B6B" />
                  )}
                  {driver.type === "motorcycle" && (
                    <Ionicons name="bicycle" size={12} color="#4ECDC4" />
                  )}
                  {driver.type === "truck" && (
                    <Ionicons name="bus" size={12} color="#1A535C" />
                  )}
                </View>
              </PointAnnotation>
            ))} */}

            {/* đường đi */}
            <ShapeSource
              id="lineSource"
              shape={{
                type: "Feature",
                properties: {},
                geometry: {
                  type: "LineString",
                  coordinates: routeInfo
                    ? routeInfo.polylineCoordinates
                    : mapPoints.map((point) => point.coordinates),
                },
              }}
            >
              <LineLayer
                id="lineLayer"
                style={{
                  lineColor: "#2E64FE",
                  lineWidth: 5,
                  lineCap: "round",
                  lineJoin: "round",
                }}
              />
            </ShapeSource>
          </MapView>

          <TouchableOpacity
            style={[styles.backButton, { top: insets.top + 10 }]}
            onPress={handleBack}
          >
            <Ionicons name="arrow-back" size={24} color="black" />
          </TouchableOpacity>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  backButton: {
    position: "absolute",
    left: 16,
    backgroundColor: "white",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#333",
  },
  driverMarker: {
    backgroundColor: "white",
    borderRadius: 20,
    padding: 8,
    borderWidth: 2,
    borderColor: "#FFE66D",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  routeInfoContainer: {
    position: "absolute",
    bottom: 20,
    alignSelf: "center",
    backgroundColor: "white",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  routeInfoText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
  },
});
