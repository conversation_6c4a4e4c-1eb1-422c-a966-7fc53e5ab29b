import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Colors } from "@/constants/Colors";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updateNote } from "@/store/slices/serviceSlice";

export default function NotesScreen() {
  const dispatch = useAppDispatch();
  const savedNote = useAppSelector((state) => state.service.note);

  const [note, setNote] = useState(savedNote);
  const insets = useSafeAreaInsets();

  // Cập nhật state local khi Redux state thay đổi
  useEffect(() => {
    setNote(savedNote);
  }, [savedNote]);

  const handleBack = () => {
    router.back();
  };

  const handleSaveNote = () => {
    // Lưu ghi chú vào Redux store
    dispatch(updateNote(note));

    // Quay lại màn hình trước đó
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        {/* Header */}
        <View style={[styles.header, { marginTop: insets.top > 0 ? 0 : 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Ghi chú</Text>
          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSaveNote}
            disabled={!note.trim()}
          >
            <Text
              style={[
                styles.saveButtonText,
                !note.trim() && styles.saveButtonTextDisabled,
              ]}
            >
              Lưu
            </Text>
          </TouchableOpacity>
        </View>

        {/* Note Input */}
        <View style={styles.noteInputContainer}>
          <TextInput
            style={styles.noteInput}
            placeholder="Nhập ghi chú cho tài xế..."
            value={note}
            onChangeText={setNote}
            multiline
            textAlignVertical="top"
            autoFocus
          />
        </View>

        {/* Suggestion Chips */}
        <View style={styles.suggestionsContainer}>
          <Text style={styles.suggestionsTitle}>Gợi ý:</Text>
          <View style={styles.chipsContainer}>
            <TouchableOpacity
              style={styles.chip}
              onPress={() => setNote("Tôi có hành lý cồng kềnh")}
            >
              <Text style={styles.chipText}>Tôi có hành lý cồng kềnh</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.chip}
              onPress={() => setNote("Tôi cần ghế trẻ em")}
            >
              <Text style={styles.chipText}>Tôi cần ghế trẻ em</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.chip}
              onPress={() => setNote("Tôi có thú cưng đi cùng")}
            >
              <Text style={styles.chipText}>Tôi có thú cưng đi cùng</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    height: 56,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.primary,
  },
  saveButtonTextDisabled: {
    color: "#ccc",
  },
  noteInputContainer: {
    flex: 1,
    padding: 16,
  },
  noteInput: {
    flex: 1,
    fontSize: 16,
    color: "#333",
    textAlignVertical: "top",
  },
  suggestionsContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  suggestionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
  },
  chipsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 16,
  },
  chip: {
    backgroundColor: "#f5f5f5",
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  chipText: {
    fontSize: 14,
    color: "#333",
  },
});
