import { getPaymentMethods, PaymentMethod } from "@/api/payment-methods.api";
import { useCallback, useState } from "react";

/**
 * Custom hook for managing payment methods
 * This hook can be shared between different screens that need to use payment methods
 */
export const usePaymentMethodsConfig = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isPaymentMethodsLoading, setIsPaymentMethodsLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("CASH"); // Default to cash

  // Memoize the fetch payment methods function to prevent unnecessary API calls
  const fetchPaymentMethods = useCallback(async () => {
    // Skip fetching if we already have payment methods
    if (paymentMethods.length > 0) {
      return;
    }

    setIsPaymentMethodsLoading(true);
    try {
      const methods = await getPaymentMethods();
      setPaymentMethods(methods);
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    } finally {
      setIsPaymentMethodsLoading(false);
    }
  }, [paymentMethods.length]);

  return {
    paymentMethods,
    isPaymentMethodsLoading,
    paymentMethod,
    setPaymentMethod,
    fetchPaymentMethods,
  };
};
