import {
  DefaultTheme,
  ThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack, Redirect } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import "react-native-reanimated";
import { AppRegistry } from "react-native";
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';

// Import our text override - this will override the default Text component
// throughout the app due to the way React Native's AppRegistry works
import "@/components/TextOverride";

// Import AuthProvider
import { AuthProvider } from "@/contexts/AuthContext";

// Import Redux store and persistor
import { store, persistor } from '@/store';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Register the main component
AppRegistry.registerComponent("main", () => RootLayout);

export default function RootLayout() {
  const [loaded] = useFonts({
    Nunito: require("../assets/fonts/Nunito.ttf"),
    NunitoItalic: require("../assets/fonts/Nunito-Italic.ttf"),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <AuthProvider>
          <ThemeProvider value={DefaultTheme}>
            <Stack screenOptions={{
              headerStyle: {
                backgroundColor: '#fff',
              },
              headerTintColor: '#000',
              headerTitleStyle: {
                fontWeight: 'bold',
              },
              headerShown: false, // Ẩn debug header cho tất cả các màn hình
            }}>
              <Stack.Screen name="(tabs)" />
              <Stack.Screen name="+not-found" />
              <Stack.Screen name="login-phone" />
              <Stack.Screen name="login-password" />
              <Stack.Screen name="register" />
              <Stack.Screen name="forgot-password" />
              <Stack.Screen name="verify-otp" />
              <Stack.Screen name="new-password" />
              <Stack.Screen name="auth" />
              <Stack.Screen name="services/local" />
              <Stack.Screen name="services/fine" options={{ gestureEnabled: true }} />
              <Stack.Screen name="route-preview" />
              <Stack.Screen name="external-form" options={{ gestureEnabled: true }} />
              <Stack.Screen name="payment-method-selection" options={{ gestureEnabled: true }} />
            </Stack>
            <StatusBar style="auto" />
          </ThemeProvider>
        </AuthProvider>
      </PersistGate>
    </Provider>
  );
}

// Redirect from root to auth screen
export function HomeRoute() {
  return <Redirect href="/auth" />;
}
